# Docker Compose Environment Variables
# Copy this file to .env and update the values as needed

# Database Configuration
POSTGRES_DB=atma_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=atma_password

# RabbitMQ Configuration
RABBITMQ_DEFAULT_USER=atma_user
RABBITMQ_DEFAULT_PASS=atma_password

# Security Keys (CHANGE THESE IN PRODUCTION!)
JWT_SECRET=atma_super_secret_jwt_key_production_2024
INTERNAL_SERVICE_KEY=internal_service_secret_production_2024

# Google AI API Key (Required for analysis-worker)
# Get this from Google AI Studio: https://aistudio.google.com/
GOOGLE_AI_API_KEY=AIzaSyAH2hc8NyXkiuiPfib63bAZgzV1t7m3rbc

# AI Configuration
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.4
USE_MOCK_MODEL=false
GEMINI_PRICING_TIER=free

# Environment
NODE_ENV=production

# Logging
LOG_LEVEL=info

# Default Token Balance for New Users
DEFAULT_TOKEN_BALANCE=2

# Analysis Token Cost
ANALYSIS_TOKEN_COST=1

# Worker Configuration
WORKER_CONCURRENCY=10

# Audit Encryption Key (CHANGE IN PRODUCTION!)
AUDIT_ENCRYPTION_KEY=your_audit_encryption_key_here_change_in_production
